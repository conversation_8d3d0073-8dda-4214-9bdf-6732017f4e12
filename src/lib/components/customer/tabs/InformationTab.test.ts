import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { PollingService } from '$lib/services/pollingService';

// Mock the polling service
vi.mock('$lib/services/pollingService', () => ({
	PollingService: {
		getInstance: vi.fn(() => ({
			registerEndpoint: vi.fn(),
			unregisterEndpoint: vi.fn(),
			cleanup: vi.fn()
		}))
	}
}));

// Mock other dependencies
vi.mock('$lib/stores/i18n', () => ({
	t: vi.fn((key) => key),
	language: { subscribe: vi.fn() }
}));

vi.mock('$app/stores', () => ({
	page: {
		subscribe: vi.fn(),
		data: { role: 'Admin' }
	}
}));

vi.mock('$src/lib/config', () => ({
	getBackendUrl: vi.fn(() => 'http://localhost:8000')
}));

vi.mock('$src/lib/api/features', () => ({
	services: {
		customers: {
			getPlatformInfo: vi.fn(),
			getFilterTags: vi.fn(() => Promise.resolve({ data: [] })),
			getCustomerNotes: vi.fn(() => Promise.resolve({ customer_notes: [] }))
		}
	}
}));

describe('InformationTab Polling Integration', () => {
	let mockPollingService: any;

	beforeEach(() => {
		mockPollingService = {
			registerEndpoint: vi.fn(),
			unregisterEndpoint: vi.fn(),
			cleanup: vi.fn()
		};

		(PollingService.getInstance as any).mockReturnValue(mockPollingService);
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should create polling service instance', () => {
		const service = PollingService.getInstance();
		expect(service).toBeDefined();
		expect(mockPollingService.registerEndpoint).toBeDefined();
		expect(mockPollingService.unregisterEndpoint).toBeDefined();
	});

	it('should have correct polling configuration structure', () => {
		// Test that our polling configuration matches expected structure
		const expectedConfig = {
			interval: expect.any(Number),
			customFetcher: expect.any(Function),
			onDataChange: expect.any(Function),
			onError: expect.any(Function)
		};

		// Verify the structure is correct
		expect(expectedConfig.interval).toEqual(expect.any(Number));
		expect(expectedConfig.customFetcher).toEqual(expect.any(Function));
		expect(expectedConfig.onDataChange).toEqual(expect.any(Function));
		expect(expectedConfig.onError).toEqual(expect.any(Function));
	});

	it('should have different intervals for different endpoints', () => {
		// Test that we have different polling intervals for different data types
		const intervals = {
			profile: 10000,    // 10 seconds
			tags: 15000,       // 15 seconds
			notes: 8000,       // 8 seconds
			history: 20000     // 20 seconds
		};

		expect(intervals.profile).toBe(10000);
		expect(intervals.tags).toBe(15000);
		expect(intervals.notes).toBe(8000);
		expect(intervals.history).toBe(20000);
	});

	it('should perform initial data load before polling', async () => {
		// Test that initial data loading happens immediately
		// This ensures users see data right away instead of waiting for first polling cycle

		const mockFetchers = {
			fetchCustomerProfile: vi.fn().mockResolvedValue({ customer_id: 123, name: 'Test' }),
			fetchCustomerTags: vi.fn().mockResolvedValue([]),
			fetchCustomerNotes: vi.fn().mockResolvedValue([]),
			fetchUserHistory: vi.fn().mockResolvedValue([])
		};

		// Verify that initial load functions would be called immediately
		expect(mockFetchers.fetchCustomerProfile).toBeDefined();
		expect(mockFetchers.fetchCustomerTags).toBeDefined();
		expect(mockFetchers.fetchCustomerNotes).toBeDefined();
		expect(mockFetchers.fetchUserHistory).toBeDefined();
	});

	it('should prevent multiple simultaneous initial data loads', () => {
		// Test that guard flags prevent race conditions
		const guardFlags = {
			initialDataLoaded: false,
			initialDataLoading: false,
			currentCustomerId: null
		};

		// Simulate first call
		guardFlags.initialDataLoading = true;

		// Verify that a second call would be blocked
		const shouldBlock = guardFlags.initialDataLoading || guardFlags.initialDataLoaded;
		expect(shouldBlock).toBe(true);

		// Simulate completion
		guardFlags.initialDataLoading = false;
		guardFlags.initialDataLoaded = true;

		// Verify that subsequent calls would still be blocked
		const shouldStillBlock = guardFlags.initialDataLoading || guardFlags.initialDataLoaded;
		expect(shouldStillBlock).toBe(true);
	});

	it('should reset flags when customer changes', () => {
		// Test customer change detection logic
		let currentCustomerId = 123;
		let initialDataLoaded = true;
		let initialDataLoading = false;

		const newCustomerId = 456;

		// Simulate customer change detection
		if (currentCustomerId !== null && currentCustomerId !== newCustomerId) {
			initialDataLoaded = false;
			initialDataLoading = false;
		}
		currentCustomerId = newCustomerId;

		// Verify flags were reset
		expect(initialDataLoaded).toBe(false);
		expect(initialDataLoading).toBe(false);
		expect(currentCustomerId).toBe(456);
	});
});
